#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
from dotenv import load_dotenv

print("=== <PERSON><PERSON><PERSON> 基础测试 ===")
print()

# 加载环境变量
load_dotenv()

# 检查GitHub Token
github_tokens = os.getenv('GITHUB_TOKENS')
if not github_tokens:
    print("❌ 错误: 未找到GITHUB_TOKENS环境变量")
    print("请在.env文件中配置GitHub Token")
    sys.exit(1)

print("✅ GitHub Token配置检查通过")
print(f"   配置的Token数量: {len(github_tokens.split(','))}")

# 检查数据目录
data_path = os.getenv('DATA_PATH', 'data')
if not os.path.exists(data_path):
    print(f"📁 创建数据目录: {data_path}")
    os.makedirs(data_path, exist_ok=True)
else:
    print(f"✅ 数据目录存在: {data_path}")

# 检查查询文件
queries_file = os.getenv('QUERIES_FILE', 'queries.txt')
if os.path.exists(queries_file):
    print(f"✅ 查询文件存在: {queries_file}")
    with open(queries_file, 'r', encoding='utf-8') as f:
        queries = [line.strip() for line in f if line.strip() and not line.startswith('#')]
    print(f"   配置的查询数量: {len(queries)}")
    for i, query in enumerate(queries[:3], 1):
        print(f"   查询{i}: {query}")
else:
    print(f"❌ 查询文件不存在: {queries_file}")

print()
print("=== 环境检查完成 ===")
print()

# 测试GitHub API连接
print("🔍 测试GitHub API连接...")
import requests

try:
    token = github_tokens.split(',')[0].strip()
    headers = {
        'Authorization': f'token {token}',
        'Accept': 'application/vnd.github.v3+json'
    }
    
    response = requests.get('https://api.github.com/user', headers=headers, timeout=10)
    
    if response.status_code == 200:
        user_info = response.json()
        print(f"✅ GitHub API连接成功")
        print(f"   用户: {user_info.get('login', 'Unknown')}")
        print(f"   剩余请求次数: {response.headers.get('X-RateLimit-Remaining', 'Unknown')}")
    else:
        print(f"❌ GitHub API连接失败: {response.status_code}")
        print(f"   响应: {response.text}")
        
except Exception as e:
    print(f"❌ GitHub API测试失败: {e}")

print()
print("=== 基础测试完成 ===")
print()
print("如果以上检查都通过，说明基础环境配置正确。")
print("现在需要安装google-generativeai包才能运行完整程序。")
print()
print("建议执行以下命令安装依赖:")
print("pip install google-generativeai python-dotenv requests")
