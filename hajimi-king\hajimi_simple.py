#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
import time
import json
import requests
from datetime import datetime
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class HajimiSimple:
    def __init__(self):
        self.github_tokens = os.getenv('GITHUB_TOKENS', '').split(',')
        self.github_tokens = [token.strip() for token in self.github_tokens if token.strip()]
        
        if not self.github_tokens:
            raise ValueError("未配置GitHub Token")
        
        self.current_token_index = 0
        self.data_path = os.getenv('DATA_PATH', 'data')
        self.queries_file = os.getenv('QUERIES_FILE', 'queries.txt')
        
        # 创建必要的目录
        os.makedirs(self.data_path, exist_ok=True)
        os.makedirs(os.path.join(self.data_path, 'keys'), exist_ok=True)
        os.makedirs(os.path.join(self.data_path, 'logs'), exist_ok=True)
        
        self.session = requests.Session()
        self.found_keys = []
        
    def get_current_token(self):
        """获取当前使用的GitHub Token"""
        return self.github_tokens[self.current_token_index]
    
    def rotate_token(self):
        """轮换到下一个Token"""
        self.current_token_index = (self.current_token_index + 1) % len(self.github_tokens)
        print(f"切换到Token {self.current_token_index + 1}")
    
    def get_headers(self):
        """获取请求头"""
        return {
            'Authorization': f'token {self.get_current_token()}',
            'Accept': 'application/vnd.github.v3+json',
            'User-Agent': 'HajimiKing/1.0'
        }
    
    def load_queries(self):
        """加载搜索查询"""
        if not os.path.exists(self.queries_file):
            print(f"查询文件不存在: {self.queries_file}")
            return []
        
        queries = []
        with open(self.queries_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#'):
                    queries.append(line)
        
        return queries
    
    def search_github_code(self, query, per_page=30):
        """搜索GitHub代码"""
        url = 'https://api.github.com/search/code'
        params = {
            'q': query,
            'per_page': per_page,
            'sort': 'indexed'
        }
        
        try:
            response = self.session.get(url, headers=self.get_headers(), params=params, timeout=30)
            
            if response.status_code == 403:
                print(f"API限制，切换Token...")
                self.rotate_token()
                return None
            elif response.status_code == 422:
                print(f"查询语法错误: {query}")
                return None
            elif response.status_code != 200:
                print(f"搜索失败: {response.status_code} - {response.text}")
                return None
            
            return response.json()
            
        except Exception as e:
            print(f"搜索异常: {e}")
            return None
    
    def extract_potential_keys(self, content):
        """从内容中提取潜在的API密钥"""
        # Gemini API密钥模式
        patterns = [
            r'AIzaSy[A-Za-z0-9_-]{33}',  # Gemini API Key
            r'AIzaSy[A-Za-z0-9_-]+',     # 更宽松的匹配
        ]
        
        keys = []
        for pattern in patterns:
            matches = re.findall(pattern, content)
            keys.extend(matches)
        
        return list(set(keys))  # 去重
    
    def get_file_content(self, download_url):
        """获取文件内容"""
        try:
            response = self.session.get(download_url, headers=self.get_headers(), timeout=30)
            if response.status_code == 200:
                return response.text
        except Exception as e:
            print(f"获取文件内容失败: {e}")
        return None
    
    def save_found_key(self, key, source_info):
        """保存找到的密钥"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 保存到文件
        key_file = os.path.join(self.data_path, 'keys', f'keys_found_{timestamp}.txt')
        with open(key_file, 'a', encoding='utf-8') as f:
            f.write(f"{key}\n")
        
        # 保存详细信息
        detail_file = os.path.join(self.data_path, 'logs', f'keys_detail_{timestamp}.log')
        with open(detail_file, 'a', encoding='utf-8') as f:
            f.write(f"时间: {datetime.now()}\n")
            f.write(f"密钥: {key}\n")
            f.write(f"来源: {source_info}\n")
            f.write("-" * 50 + "\n")
        
        self.found_keys.append(key)
        print(f"✅ 找到密钥: {key[:20]}...")
    
    def process_search_results(self, results, query):
        """处理搜索结果"""
        if not results or 'items' not in results:
            return
        
        print(f"找到 {len(results['items'])} 个文件")
        
        for item in results['items']:
            try:
                # 获取文件内容
                content = self.get_file_content(item['html_url'])
                if not content:
                    continue
                
                # 提取潜在密钥
                keys = self.extract_potential_keys(content)
                
                for key in keys:
                    source_info = {
                        'repository': item['repository']['full_name'],
                        'file_path': item['path'],
                        'html_url': item['html_url'],
                        'query': query
                    }
                    
                    if key not in self.found_keys:
                        self.save_found_key(key, json.dumps(source_info, ensure_ascii=False))
                
                # 避免请求过快
                time.sleep(1)
                
            except Exception as e:
                print(f"处理文件失败: {e}")
                continue
    
    def run(self):
        """运行主程序"""
        print("🎪 Hajimi King 简化版启动")
        print(f"配置的Token数量: {len(self.github_tokens)}")
        print(f"数据目录: {self.data_path}")
        print("-" * 50)
        
        queries = self.load_queries()
        if not queries:
            print("❌ 没有找到有效的查询配置")
            return
        
        print(f"加载了 {len(queries)} 个查询")
        
        try:
            for i, query in enumerate(queries, 1):
                print(f"\n🔍 执行查询 {i}/{len(queries)}: {query}")
                
                results = self.search_github_code(query)
                if results:
                    self.process_search_results(results, query)
                
                # 查询间隔
                time.sleep(2)
                
        except KeyboardInterrupt:
            print("\n\n⏹️ 用户中断程序")
        except Exception as e:
            print(f"\n❌ 程序异常: {e}")
        
        print(f"\n🎉 程序结束，共找到 {len(self.found_keys)} 个密钥")
        if self.found_keys:
            print("密钥已保存到 data/keys/ 目录")

if __name__ == "__main__":
    try:
        hajimi = HajimiSimple()
        hajimi.run()
    except Exception as e:
        print(f"启动失败: {e}")
        input("按回车键退出...")
