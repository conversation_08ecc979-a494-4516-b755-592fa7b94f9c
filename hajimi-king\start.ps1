Write-Host "正在启动 Haji<PERSON> King..." -ForegroundColor Green
Write-Host ""

# 检查Python
try {
    $pythonVersion = python --version 2>&1
    Write-Host "Python环境检查通过: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "错误: 未找到Python" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

# 安装依赖
Write-Host "正在安装依赖包..." -ForegroundColor Yellow
try {
    python -m pip install google-generativeai python-dotenv requests --quiet --disable-pip-version-check
    Write-Host "依赖安装完成" -ForegroundColor Green
} catch {
    Write-Host "警告: 依赖安装可能失败，尝试继续运行..." -ForegroundColor Yellow
}

# 检查配置文件
if (-not (Test-Path ".env")) {
    Write-Host "错误: 未找到.env文件，请确保已配置GitHub Token" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

# 创建data目录
if (-not (Test-Path "data")) {
    Write-Host "创建data目录..." -ForegroundColor Yellow
    New-Item -ItemType Directory -Name "data" | Out-Null
}

Write-Host ""
Write-Host "正在启动程序..." -ForegroundColor Green
Write-Host "按 Ctrl+C 停止程序" -ForegroundColor Yellow
Write-Host ""

# 运行程序
try {
    python app/hajimi_king.py
} catch {
    Write-Host "程序运行出错: $_" -ForegroundColor Red
}

Write-Host ""
Write-Host "程序已退出" -ForegroundColor Yellow
Read-Host "按任意键退出"
