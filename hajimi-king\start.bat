@echo off
echo 正在启动 <PERSON><PERSON><PERSON> King...
echo.

REM 检查Python是否可用
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请确保Python已安装并添加到PATH
    pause
    exit /b 1
)

echo Python环境检查通过
echo.

REM 尝试安装依赖
echo 正在安装依赖包...
python -m pip install google-generativeai python-dotenv requests --quiet --disable-pip-version-check

if errorlevel 1 (
    echo 警告: 依赖安装可能失败，尝试继续运行...
    echo.
)

REM 检查.env文件
if not exist ".env" (
    echo 错误: 未找到.env文件，请确保已配置GitHub Token
    pause
    exit /b 1
)

REM 检查data目录
if not exist "data" (
    echo 创建data目录...
    mkdir data
)

echo 正在启动程序...
echo 按 Ctrl+C 停止程序
echo.

REM 运行程序
python app/hajimi_king.py

echo.
echo 程序已退出
pause
