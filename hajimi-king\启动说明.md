# Hajimi King 启动说明

## 项目已成功克隆到本地

项目位置：`d:\xx\hajimi-king`

## 当前状态

✅ 项目已克隆  
✅ 配置文件已复制（.env 和 queries.txt）  
✅ 数据目录已创建  
⚠️ 需要配置GitHub Token才能正常运行  

## 下一步操作

### 1. 配置GitHub Token（必须）

编辑 `.env` 文件，在第4行添加你的GitHub Token：

```bash
GITHUB_TOKENS=你的GitHub_Token_这里
```

**获取GitHub Token的步骤：**
1. 访问 https://github.com/settings/tokens
2. 点击 "Generate new token" -> "Generate new token (classic)"
3. 选择权限：只需要 `public_repo` 权限
4. 复制生成的token到 `.env` 文件中

### 2. 安装依赖

由于环境问题，建议使用以下方法之一：

#### 方法一：使用conda（推荐）
```bash
# 创建新环境
conda create -n hajimi-king python=3.11 -y

# 激活环境
conda activate hajimi-king

# 安装依赖
pip install google-generativeai==0.8.5 python-dotenv==1.1.1 requests==2.32.4
```

#### 方法二：使用Docker（如果已安装Docker）
```bash
# 构建镜像
docker build -t hajimi-king:0.0.1 .

# 运行容器
docker-compose up -d
```

### 3. 运行程序

```bash
# 确保在正确的环境中
conda activate hajimi-king

# 运行程序
python app/hajimi_king.py
```

### 4. 查看结果

程序运行后，结果会保存在 `data` 目录中：
- `data/keys/keys_valid_*.txt` - 找到的有效密钥
- `data/logs/keys_valid_detail_*.log` - 详细日志

## 注意事项

1. **必须配置GitHub Token**，否则程序无法运行
2. 建议使用代理，避免IP被封
3. 程序会持续运行，按 Ctrl+C 停止
4. 首次运行可能需要一些时间来搜索和验证密钥

## 故障排除

如果遇到依赖安装问题：
1. 确保网络连接正常
2. 尝试使用不同的Python环境
3. 考虑使用Docker方式运行

## 项目功能

- 🔍 搜索GitHub中的Gemini API密钥
- 🌐 支持代理轮换
- 📊 增量扫描，支持断点续传
- 🚫 智能过滤文档和测试文件
- 🔄 支持向外部服务同步密钥
